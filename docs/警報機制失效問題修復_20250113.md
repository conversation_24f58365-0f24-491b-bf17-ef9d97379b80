# 警報機制失效問題修復記錄

**修改時間：** 2025年1月13日  
**問題描述：** 警報發生時，有時候頁面跳轉及聲音播放機制會失效，LOG有紀錄但畫面跳轉及聲音都沒觸發  
**修改人員：** Augment Agent  

## 根本問題

經過詳細分析，發現警報機制失效的根本原因有兩個：

### 問題1：m_max 沒有被更新
當警報觸發時（`obj.max > m_max`），程式碼執行了警報處理，但是**沒有更新 `m_max` 的值**，導致：
- 同一個警報會被重複處理
- 在頻繁跳動時可能導致頁面跳轉衝突
- 警報處理邏輯混亂

### 問題2：AJAX 錯誤處理呼叫錯誤函數
在錯誤處理中呼叫了錯誤的函數，導致警報檢查循環中斷。

## 修復內容

### 修復1：更新 m_max 值

**檔案：** `web/com_whome-1.0.0/site/views/roots/tmpl/default.php`
**位置：** 第925-934行

```javascript
// 修改前
if(obj.max > m_max)
{
    alarmItemFunc(obj); 
}

// 修改後
if(obj.max > m_max)
{
    alarmItemFunc(obj); 
    m_max = obj.max;  // 修正：更新 m_max 避免重複觸發
}
```

**檔案：** `web/com_floor-1.0.0/site/views/sroots/tmpl/default.php`
**位置：** 第2530-2540行

```javascript
// 修改前
if(obj.max > m_max)
{
    do_device_state(obj);
}

// 修改後
if(obj.max > m_max)
{
    do_device_state(obj);
    m_max = obj.max;  // 修正：更新 m_max 避免重複觸發
}
```

### 修復2：修正 AJAX 錯誤處理函數

**檔案：** `web/com_whome-1.0.0/site/views/roots/tmpl/default.php`
**位置：** 第942-946行

```javascript
// 修改前
error: function(response) {
    console.log("error7");
    setTimeout(statusFunc, timeout_log*10);
}

// 修改後
error: function(response) {
    console.log("error7");
    setTimeout(getLogStatus, timeout_log*10);  // 修正：呼叫正確的函數
}
```

**檔案：** `web/com_floor-1.0.0/site/views/sroots/tmpl/default.php`
**位置：** 第2548-2552行

```javascript
// 修改前
error: function(response) {
    console.log("error7");
    setTimeout(statusFunc, timeout_log*10);
}

// 修改後
error: function(response) {
    console.log("error7");
    setTimeout(getLogStatus, timeout_log*10);  // 修正：呼叫正確的函數
}
```

## 修復效果

1. **避免重複觸發**：更新 `m_max` 後，同一個警報不會被重複處理
2. **保持檢查循環**：修正錯誤處理函數後，AJAX 錯誤時仍能繼續警報檢查
3. **提高穩定性**：減少頻繁跳動時的衝突問題
4. **確保警報響應**：警報觸發後能正確執行頁面跳轉和聲音播放

## 測試建議

1. 觸發警報並觀察是否正常跳轉和播放聲音
2. 測試頻繁警報觸發的情況
3. 模擬網路錯誤情況，確認警報檢查循環不會中斷
4. 檢查瀏覽器開發者工具中是否還有 JavaScript 錯誤

## 注意事項

這些修復解決了警報機制的核心問題，但如果仍有問題，可能需要進一步檢查：
- 網路連線穩定性
- 伺服器回應時間
- 瀏覽器相容性
- 音效檔案是否存在且可播放
